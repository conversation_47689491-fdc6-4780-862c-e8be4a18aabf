generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Image {
  id              String               @id
  url             String
  publicId        String
  title           String?
  description     String?
  tags            String?
  groupId         String?
  uploadedAt      DateTime             @default(now())
  primaryProvider String               @default("cloudinary")
  backupProvider  String?
  storageMetadata String?              @db.Text
  storageRecords  ImageStorageRecord[]
  group           Group?               @relation(fields: [groupId], references: [id])

  @@index([groupId], map: "images_groupId_fkey")
  @@map("images")
}

model Group {
  id          String   @id
  name        String   @unique
  description String?
  imageCount  Int      @default(0)
  createdAt   DateTime @default(now())
  images      Image[]

  @@map("groups")
}

model APIConfig {
  id                   String   @id @default("default")
  isEnabled            Boolean  @default(true)
  defaultScope         String   @default("all")
  defaultGroups        String?  @db.Text
  allowedParameters    String?  @db.Text
  enableDirectResponse Boolean  @default(false)
  updatedAt            DateTime @default(now()) @updatedAt

  @@map("api_configs")
}

model Counter {
  id    String @id
  value Int    @default(0)

  @@map("counters")
}

model ImageStorageRecord {
  id         String   @id @default(cuid())
  imageId    String
  provider   String
  identifier String
  url        String
  metadata   String?  @db.Text
  status     String   @default("active")
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  image      Image    @relation(fields: [imageId], references: [id], onDelete: Cascade)

  @@index([imageId])
  @@index([provider])
  @@index([status])
  @@map("image_storage_records")
}

model StorageConfig {
  id                  String   @id @default("default")
  primaryProvider     String   @default("cloudinary")
  backupProvider      String?
  failoverEnabled     Boolean  @default(true)
  retryAttempts       Int      @default(3)
  retryDelay          Int      @default(1000)
  healthCheckInterval Int      @default(300)
  enableBackupUpload  Boolean  @default(false)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt

  @@map("storage_configs")
}

model SystemLog {
  id        String   @id @default(cuid())
  timestamp DateTime @default(now())
  level     Int
  message   String   @db.Text
  context   String?  @db.Text
  error     String?  @db.Text
  userId    String?
  requestId String?
  ip        String?
  userAgent String?  @db.Text
  type      String?

  @@index([timestamp])
  @@index([level])
  @@index([type])
  @@index([userId])
  @@map("system_logs")
}
