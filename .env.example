# 数据库配置
DATABASE_URL="mysql://username:password@host:port/database?sslaccept=strict"

# Cloudinary 配置
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# tgState 配置
TGSTATE_BASE_URL=https://you-admin
# TGSTATE_PASSWORD=none  # 由于密码鉴权存在 bug，暂时不使用密码
TGSTATE_TIMEOUT=30000

# 多图床配置
PRIMARY_STORAGE_PROVIDER=cloudinary
BACKUP_STORAGE_PROVIDER=tgstate
FAILOVER_STRATEGY=retry_then_failover
STORAGE_RETRY_ATTEMPTS=3
STORAGE_RETRY_DELAY=1000
STORAGE_HEALTH_CHECK_INTERVAL=300
ENABLE_BACKUP_UPLOAD=false

# 管理员认证
ADMIN_PASSWORD=your_admin_password

# 环境设置
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000