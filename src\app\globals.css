@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* 透明面板样式 */
.transparent-panel {
  background: rgba(255, 255, 255, var(--panel-opacity, 0.9));
  backdrop-filter: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .transparent-panel {
  background: rgba(0, 0, 0, var(--panel-opacity, 0.9));
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 确保文字在透明背景下的可读性 */
.panel-text {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dark .panel-text {
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
}