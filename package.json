{"name": "random-image-api", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:replit": "npm install --include=dev && npx prisma generate && next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "postinstall": "prisma generate"}, "dependencies": {"@prisma/client": "^6.12.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "cloudinary": "^2.2.0", "clsx": "^2.1.1", "dotenv": "^17.2.0", "eslint": "^8", "eslint-config-next": "14.2.5", "lucide-react": "^0.525.0", "mysql2": "^3.14.2", "next": "14.2.5", "postcss": "^8", "prisma": "^6.12.0", "react": "^18", "react-dom": "^18", "sharp": "^0.34.3", "swr": "^2.2.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.1", "typescript": "^5", "zod": "^3.23.8"}, "devDependencies": {"@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.0", "@types/jest": "^29.5.12", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0"}}